import React, { useState } from 'react';
import './App.css'; // Buat file App.css untuk styling

function App() {
  const [prompt, setPrompt] = useState('');
  const [response, setResponse] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setResponse('');
    setError(null);

    try {
      const res = await fetch('http://localhost:8000/generate', { // Ganti dengan URL backend Anda
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ prompt }),
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.error || `HTTP error! status: ${res.status}`);
      }

      const data = await res.json();
      // Ollama mengembalikan respons dalam format { model: ..., created_at: ..., response: "...", done: ... }
      setResponse(data.response);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="App">
      <header className="App-header">
        <h1>Tanya Llama</h1>
        <form onSubmit={handleSubmit}>
          <textarea
            rows="5"
            cols="50"
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            placeholder="Type your prompt here..."
            disabled={loading}
          />
          <br />
          <button type="submit" disabled={loading}>
            {loading ? 'Generating...' : 'Generate Response'}
          </button>
        </form>

        {error && <p className="error-message">Error: {error}</p>}

        {response && (
          <div className="response-box">
            <h2>Llama 3 Response:</h2>
            <p>{response}</p>
          </div>
        )}
      </header>
    </div>
  );
}

export default App;