.App {
  text-align: center;
  padding: 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.App-header {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 50%, #4facfe 100%);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding: 40px 20px;
  color: white;
  position: relative;
}

/* Styling untuk judul */
h1 {
  font-size: 3.5rem;
  font-weight: bold;
  margin-bottom: 30px;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
  background: linear-gradient(45deg, #fff, #f0f8ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Container untuk form di tengah */
form {
  background: rgba(255, 255, 255, 0.95);
  padding: 30px;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
  margin: 20px auto;
  max-width: 600px;
  backdrop-filter: blur(10px);
}

textarea {
  width: 100%;
  max-width: 500px;
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 15px;
  border: 3px solid #4facfe;
  font-size: 16px;
  font-family: inherit;
  resize: vertical;
  transition: all 0.3s ease;
  background: #f8f9ff;
}

textarea:focus {
  outline: none;
  border-color: #f5576c;
  box-shadow: 0 0 15px rgba(245, 87, 108, 0.3);
  transform: scale(1.02);
}

button {
  padding: 15px 30px;
  font-size: 18px;
  font-weight: bold;
  background: linear-gradient(45deg, #4facfe, #00f2fe);
  color: white;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
  text-transform: uppercase;
  letter-spacing: 1px;
}

button:disabled {
  background: linear-gradient(45deg, #cccccc, #999999);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  animation: pulse 1.5s infinite;
}

button:hover:not(:disabled) {
  background: linear-gradient(45deg, #f5576c, #f093fb);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(245, 87, 108, 0.4);
}

button:active:not(:disabled) {
  transform: translateY(0);
}

/* Animasi loading */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Response box styling - ditempatkan di bawah */
.response-box {
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  padding: 25px;
  border-radius: 20px;
  margin: 30px auto;
  max-width: 800px;
  text-align: left;
  word-wrap: break-word;
  box-shadow: 0 10px 30px rgba(0,0,0,0.15);
  backdrop-filter: blur(10px);
  border: 2px solid rgba(79, 172, 254, 0.3);
  animation: fadeIn 0.5s ease-out;
}

.response-box h2 {
  color: #4facfe;
  font-size: 1.5rem;
  margin-bottom: 15px;
  text-align: center;
}

.response-box p {
  font-size: 16px;
  line-height: 1.6;
  color: #444;
  background: #f8f9ff;
  padding: 15px;
  border-radius: 10px;
  border-left: 4px solid #4facfe;
}

.error-message {
  color: #ff4757;
  background: rgba(255, 71, 87, 0.1);
  padding: 15px;
  border-radius: 10px;
  margin: 20px auto;
  max-width: 600px;
  border: 2px solid #ff4757;
  font-weight: bold;
}

/* Responsive design */
@media (max-width: 768px) {
  h1 {
    font-size: 2.5rem;
  }

  form {
    margin: 10px;
    padding: 20px;
  }

  textarea {
    width: 100%;
    font-size: 14px;
  }

  button {
    padding: 12px 25px;
    font-size: 16px;
  }

  .response-box {
    margin: 20px 10px;
    padding: 20px;
  }
}

/* Animasi fade in untuk response */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Efek hover untuk textarea */
textarea::placeholder {
  color: #999;
  font-style: italic;
}